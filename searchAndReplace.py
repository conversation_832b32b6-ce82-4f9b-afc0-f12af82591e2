import inkex

class ReplaceJulWithAug(inkex.EffectExtension):
    def effect(self):
        for text_elem in self.svg.xpath('//svg:text', namespaces=inkex.NSS):
            if text_elem.text:
                text_elem.text = text_elem.text.replace('Jul', 'Aug')
            # Also check for tspans inside text elements
            for tspan in text_elem.xpath('.//svg:tspan', namespaces=inkex.NSS):
                if tspan.text:
                    tspan.text = tspan.text.replace('Jul', 'Aug')

if __name__ == '__main__':
    ReplaceJulWithAug().run()